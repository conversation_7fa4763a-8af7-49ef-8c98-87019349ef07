#!/usr/bin/env python3
"""
Test script to verify all D³AFD algorithms can be imported correctly
"""

import os
import sys
import traceback

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test importing all algorithm modules"""
    print("Testing D³AFD Multi-Algorithm Framework Imports...")
    print("=" * 60)
    
    # Test basic imports
    tests = [
        ("PyTorch", lambda: __import__('torch')),
        ("Transformers", lambda: __import__('transformers')),
        ("NumPy", lambda: __import__('numpy')),
        ("Pandas", lambda: __import__('pandas')),
        ("YAML", lambda: __import__('yaml')),
    ]
    
    # Test algorithm imports
    algorithm_tests = [
        ("DKDM Algorithm", lambda: __import__('src.algorithms.dkdm_distillation', fromlist=['DKDMDistillationTrainer'])),
        ("DiffDFKD Algorithm", lambda: __import__('src.algorithms.diffdfkd_distillation', fromlist=['DiffDFKDDistillationTrainer'])),
        ("DiffKD Algorithm", lambda: __import__('src.algorithms.diffkd_distillation', fromlist=['DiffKDDistillationTrainer'])),
        ("Server Module", lambda: __import__('src.federated.server', fromlist=['FederatedServer', 'DistillationAlgorithm'])),
    ]
    
    # Test core imports
    print("Testing Core Dependencies:")
    print("-" * 30)
    
    for name, import_func in tests:
        try:
            import_func()
            print(f"✅ {name:15} - OK")
        except Exception as e:
            print(f"❌ {name:15} - FAILED: {e}")
    
    print("\nTesting Algorithm Modules:")
    print("-" * 30)
    
    # Test algorithm imports
    for name, import_func in algorithm_tests:
        try:
            import_func()
            print(f"✅ {name:20} - OK")
        except Exception as e:
            print(f"❌ {name:20} - FAILED: {e}")
            print(f"   Error details: {str(e)}")
    
    print("\nTesting Algorithm Classes:")
    print("-" * 30)
    
    # Test algorithm class instantiation
    try:
        from src.federated.server import DistillationAlgorithm
        algorithms = list(DistillationAlgorithm)
        print(f"✅ Available algorithms: {[alg.value for alg in algorithms]}")
    except Exception as e:
        print(f"❌ Algorithm enum import failed: {e}")
    
    # Test individual algorithm classes
    algorithm_classes = [
        ("DKDMDistillationTrainer", "src.algorithms.dkdm_distillation"),
        ("DiffDFKDDistillationTrainer", "src.algorithms.diffdfkd_distillation"),
        ("DiffKDDistillationTrainer", "src.algorithms.diffkd_distillation"),
    ]
    
    for class_name, module_name in algorithm_classes:
        try:
            module = __import__(module_name, fromlist=[class_name])
            cls = getattr(module, class_name)
            print(f"✅ {class_name:25} - Class available")
        except Exception as e:
            print(f"❌ {class_name:25} - FAILED: {e}")

def test_config_loading():
    """Test configuration loading"""
    print("\nTesting Configuration:")
    print("-" * 30)
    
    try:
        import yaml
        config_path = "configs/d3afd_multi_algorithm.yaml"
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Check required sections
            required_sections = ['experiment', 'training', 'model', 'data']
            for section in required_sections:
                if section in config:
                    print(f"✅ Config section '{section}' - OK")
                else:
                    print(f"❌ Config section '{section}' - MISSING")
            
            # Check algorithm configuration
            algorithm = config.get('training', {}).get('distillation_algorithm', 'unknown')
            print(f"✅ Default algorithm: {algorithm}")
            
        else:
            print(f"❌ Config file not found: {config_path}")
            
    except Exception as e:
        print(f"❌ Config loading failed: {e}")

def test_quick_functionality():
    """Test basic functionality without full training"""
    print("\nTesting Basic Functionality:")
    print("-" * 30)
    
    try:
        # Test config creation
        from src.utils.config import Config
        test_config = {
            'experiment': {'device': 'cpu'},
            'training': {'distillation_algorithm': 'dkdm'},
            'model': {'hidden_size': 768}
        }
        config = Config(test_config)
        print("✅ Config creation - OK")
        
        # Test algorithm enum
        from src.federated.server import DistillationAlgorithm
        alg = DistillationAlgorithm.DKDM
        print(f"✅ Algorithm enum access - OK ({alg.value})")
        
        # Test algorithm info methods
        from src.algorithms.dkdm_distillation import DKDMDistillationTrainer
        trainer = DKDMDistillationTrainer(config)
        info = trainer.get_algorithm_info()
        print(f"✅ Algorithm info - OK ({info['name']})")
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        traceback.print_exc()

def main():
    """Run all tests"""
    print("D³AFD Multi-Algorithm Framework - Import Test")
    print("=" * 60)
    
    test_imports()
    test_config_loading()
    test_quick_functionality()
    
    print("\n" + "=" * 60)
    print("Import test completed!")
    print("\nIf you see any ❌ errors above, please:")
    print("1. Install missing dependencies: pip install -r requirements_multi_algorithm.txt")
    print("2. Check that src/ directory is in your Python path")
    print("3. Verify all algorithm files are present")
    print("\nTo run a quick algorithm test:")
    print("python quick_test_algorithms.py --algorithm dkdm")

if __name__ == "__main__":
    main()
